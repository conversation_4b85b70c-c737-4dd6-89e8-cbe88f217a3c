'use client';

import React, { useState, useCallback, useEffect } from 'react';
import TextEditor from '@/components/common/TextEditor';

const LocationContactsManager = ({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);
  const [localFormData, setLocalFormData] = useState({
    title: '',
    body: '',
    details: ''
  });
  const [validationErrors, setValidationErrors] = useState({});

  // Update local form data when formData changes
  useEffect(() => {
    if (formData) {
      setLocalFormData({
        title: formData.title || '',
        body: formData.body || '',
        details: formData.details || ''
      });
    }
  }, [formData]);

  // Enhanced validation function
  const validateLocalData = useCallback(() => {
    const newErrors = {};

    // Title validation
    if (!localFormData.title?.trim()) {
      newErrors.title = 'Title is required';
    } else if (localFormData.title.length > 200) {
      newErrors.title = 'Title cannot exceed 200 characters';
    }

    // Body validation
    if (!localFormData.body?.trim()) {
      newErrors.body = 'Body content is required';
    } else if (localFormData.body.length > 5000) {
      newErrors.body = 'Body content cannot exceed 5000 characters';
    }

    // Details validation
    if (!localFormData.details?.trim()) {
      newErrors.details = 'Details are required';
    } else if (localFormData.details.length > 5000) {
      newErrors.details = 'Details cannot exceed 5000 characters';
    }

    setValidationErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [localFormData]);

  // Handle local form changes when in edit mode
  const handleLocalChange = useCallback((content, field) => {
    setLocalFormData(prev => ({
      ...prev,
      [field]: content
    }));

    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  }, [validationErrors]);

  // Fetch fresh data when entering edit mode
  const fetchLatestData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages');
      const data = await response.json();

      if (data.success) {
        const locationData = data.data?.locationAndcontacts || {};
        const freshFormData = {
          title: locationData.title || '',
          body: locationData.body || '',
          details: locationData.details || ''
        };
        setLocalFormData(freshFormData);
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('Error fetching latest location data:', error);
      setDataError('Failed to load latest data. Using current values.');
      // Fallback to current form data
      const currentFormData = {
        title: formData?.title || '',
        body: formData?.body || '',
        details: formData?.details || ''
      };
      setLocalFormData(currentFormData);
    } finally {
      setIsLoadingData(false);
    }
  }, [formData]);

  // Handle edit mode toggle
  const handleEdit = useCallback(async () => {
    setIsEditing(true);
    setValidationErrors({});
    await fetchLatestData();
  }, [fetchLatestData]);

  // Enhanced save handler with better validation
  const handleSave = useCallback(async () => {
    try {
      // Validate all fields
      if (!validateLocalData()) {
        return; // Validation errors are already set in state
      }

      // Update the parent form data first (synchronously)
      Object.keys(localFormData).forEach(field => {
        onQuillChange(localFormData[field], 'locationAndcontacts', field);
      });

      // Wait a brief moment for state updates to propagate
      await new Promise(resolve => setTimeout(resolve, 100));

      // Save to API with the current local form data
      await onSectionSave('locationAndcontacts', localFormData);
      setIsEditing(false);
      setValidationErrors({});
    } catch (error) {
      console.error('Error saving location & contacts:', error);
      setDataError('Failed to save changes. Please try again.');
      // Don't exit edit mode if save failed
    }
  }, [localFormData, onQuillChange, onSectionSave, validateLocalData]);

  // Handle cancel edit
  const handleCancel = useCallback(() => {
    // Reset to current form data when canceling
    setLocalFormData({
      title: formData?.title || '',
      body: formData?.body || '',
      details: formData?.details || ''
    });
    setValidationErrors({});
    setDataError(null);
    setIsEditing(false);
  }, [formData]);

  // Enhanced delete handler with better confirmation
  const handleDelete = useCallback(async () => {
    try {
      // Clear all fields
      const emptyData = {
        title: '',
        body: '',
        details: ''
      };

      Object.keys(emptyData).forEach(field => {
        onQuillChange(emptyData[field], 'locationAndcontacts', field);
      });

      await onSectionSave('locationAndcontacts', emptyData);
      setShowDeleteConfirm(false);
      setIsEditing(false);
      setValidationErrors({});
    } catch (error) {
      console.error('Error deleting location & contacts:', error);
      setDataError('Failed to delete content. Please try again.');
    }
  }, [onQuillChange, onSectionSave]);

  const currentData = isEditing ? localFormData : formData;
  const hasContent = formData.title || formData.body || formData.details;

  // Character count helper
  const getCharacterCount = (text) => {
    return text ? text.replace(/<[^>]*>/g, '').length : 0;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Location & Contacts Management</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage your location information, contact details, and additional content for visitors.
          </p>
        </div>
        <div className="flex space-x-2">
          {!isEditing ? (
            <>
              <button
                type="button"
                onClick={handleEdit}
                disabled={isLoadingData}
                className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoadingData ? 'Loading...' : hasContent ? 'Edit Content' : 'Create Content'}
              </button>
              {hasContent && (
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(true)}
                  className="px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Delete All
                </button>
              )}
              <button
                type="button"
                onClick={onSectionSave}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Saving...' : 'Save Section'}
              </button>
            </>
          ) : (
            <>
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSave}
                disabled={isLoading || Object.keys(validationErrors).length > 0}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </>
          )}
        </div>
      </div>

      {/* Enhanced Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">Delete All Content</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to permanently delete all Location & Contacts content?
                  This will remove:
                </p>
                <ul className="text-sm text-gray-600 mt-2 text-left">
                  <li>• Page title</li>
                  <li>• Body content</li>
                  <li>• Contact details</li>
                </ul>
                <p className="text-sm text-red-600 mt-2 font-medium">
                  This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-center space-x-3 mt-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Deleting...' : 'Delete All Content'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Error Display */}
      {dataError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-red-800">Error</h4>
              <p className="text-sm text-red-700 mt-1">{dataError}</p>
              <button
                onClick={() => setDataError(null)}
                className="text-sm text-red-600 hover:text-red-800 underline mt-2"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Loading State */}
      {isLoadingData && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
            <div>
              <p className="text-sm font-medium text-blue-800">Loading latest data...</p>
              <p className="text-xs text-blue-600 mt-1">Fetching the most recent content from the database</p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Content Display/Edit Form */}
      {!hasContent && !isEditing ? (
        <div className="text-center py-16 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">No Location & Contacts Content</h4>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">
            Create your location and contact information to help visitors find and connect with you.
          </p>
          <button
            onClick={handleEdit}
            disabled={isLoadingData}
            className="px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoadingData ? 'Loading...' : 'Create Location & Contacts Content'}
          </button>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Enhanced Title Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Page Title *
              </label>
              {isEditing && (
                <span className="text-xs text-gray-500">
                  {getCharacterCount(currentData.title)}/200 characters
                </span>
              )}
            </div>
            <p className="text-xs text-gray-600 mb-3">
              The main heading that appears at the top of your location and contacts page.
            </p>
            {isEditing ? (
              <div>
                <TextEditor
                  key={`title-edit-${isEditing}`}
                  value={currentData.title}
                  onChange={(content) => handleLocalChange(content, 'title')}
                  placeholder="Enter a compelling title for your location page (e.g., 'Visit Our Beautiful Island Paradise')"
                  style={{ minHeight: '100px' }}
                  className={`border rounded-md ${
                    validationErrors.title || errors['locationAndcontacts.title']
                      ? 'border-red-500'
                      : 'border-gray-300'
                  }`}
                />
                {(validationErrors.title || errors['locationAndcontacts.title']) && (
                  <p className="mt-2 text-sm text-red-600">
                    {validationErrors.title || errors['locationAndcontacts.title']}
                  </p>
                )}
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border min-h-[60px] flex items-center">
                <div
                  dangerouslySetInnerHTML={{
                    __html: currentData.title || '<span class="text-gray-400 italic">No title set</span>'
                  }}
                  className="w-full"
                />
              </div>
            )}
          </div>

          {/* Enhanced Body Content Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Main Content *
              </label>
              {isEditing && (
                <span className="text-xs text-gray-500">
                  {getCharacterCount(currentData.body)}/5000 characters
                </span>
              )}
            </div>
            <p className="text-xs text-gray-600 mb-3">
              The main content describing your location, how to get there, and general information for visitors.
            </p>
            {isEditing ? (
              <div>
                <TextEditor
                  key={`body-edit-${isEditing}`}
                  value={currentData.body}
                  onChange={(content) => handleLocalChange(content, 'body')}
                  placeholder="Describe your location, accessibility, nearby landmarks, transportation options, and any other relevant information for visitors..."
                  style={{ minHeight: '200px' }}
                  className={`border rounded-md ${
                    validationErrors.body || errors['locationAndcontacts.body']
                      ? 'border-red-500'
                      : 'border-gray-300'
                  }`}
                />
                {(validationErrors.body || errors['locationAndcontacts.body']) && (
                  <p className="mt-2 text-sm text-red-600">
                    {validationErrors.body || errors['locationAndcontacts.body']}
                  </p>
                )}
                <div className="mt-2 text-xs text-gray-500">
                  💡 Tip: Use formatting tools to make your content more readable. Add links to maps, contact information, or related pages.
                </div>
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border min-h-[120px] flex items-start">
                <div
                  dangerouslySetInnerHTML={{
                    __html: currentData.body || '<span class="text-gray-400 italic">No main content set</span>'
                  }}
                  className="w-full prose prose-sm max-w-none"
                />
              </div>
            )}
          </div>

          {/* Enhanced Contact Details Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Contact Details *
              </label>
              {isEditing && (
                <span className="text-xs text-gray-500">
                  {getCharacterCount(currentData.details)}/5000 characters
                </span>
              )}
            </div>
            <p className="text-xs text-gray-600 mb-3">
              Specific contact information, phone numbers, email addresses, operating hours, and any additional details visitors need.
            </p>
            {isEditing ? (
              <div>
                <TextEditor
                  key={`details-edit-${isEditing}`}
                  value={currentData.details}
                  onChange={(content) => handleLocalChange(content, 'details')}
                  placeholder="Include contact information such as:&#10;• Phone numbers&#10;• Email addresses&#10;• Physical address&#10;• Operating hours&#10;• Emergency contacts&#10;• Special instructions"
                  style={{ minHeight: '200px' }}
                  className={`border rounded-md ${
                    validationErrors.details || errors['locationAndcontacts.details']
                      ? 'border-red-500'
                      : 'border-gray-300'
                  }`}
                />
                {(validationErrors.details || errors['locationAndcontacts.details']) && (
                  <p className="mt-2 text-sm text-red-600">
                    {validationErrors.details || errors['locationAndcontacts.details']}
                  </p>
                )}
                <div className="mt-2 text-xs text-gray-500">
                  💡 Tip: Use the email and link tools in the editor to make contact information clickable for visitors.
                </div>
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border min-h-[120px] flex items-start">
                <div
                  dangerouslySetInnerHTML={{
                    __html: currentData.details || '<span class="text-gray-400 italic">No contact details set</span>'
                  }}
                  className="w-full prose prose-sm max-w-none"
                />
              </div>
            )}
          </div>

          {/* Form Summary (only shown in edit mode) */}
          {isEditing && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Content Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                <div>
                  <span className="font-medium text-blue-800">Title:</span>
                  <span className={`ml-1 ${getCharacterCount(currentData.title) > 200 ? 'text-red-600' : 'text-blue-700'}`}>
                    {getCharacterCount(currentData.title)}/200 chars
                  </span>
                </div>
                <div>
                  <span className="font-medium text-blue-800">Main Content:</span>
                  <span className={`ml-1 ${getCharacterCount(currentData.body) > 5000 ? 'text-red-600' : 'text-blue-700'}`}>
                    {getCharacterCount(currentData.body)}/5000 chars
                  </span>
                </div>
                <div>
                  <span className="font-medium text-blue-800">Contact Details:</span>
                  <span className={`ml-1 ${getCharacterCount(currentData.details) > 5000 ? 'text-red-600' : 'text-blue-700'}`}>
                    {getCharacterCount(currentData.details)}/5000 chars
                  </span>
                </div>
              </div>
              {Object.keys(validationErrors).length > 0 && (
                <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
                  <p className="text-xs text-red-800 font-medium">Please fix the following errors before saving:</p>
                  <ul className="text-xs text-red-700 mt-1 list-disc list-inside">
                    {Object.entries(validationErrors).map(([field, error]) => (
                      <li key={field}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LocationContactsManager;
